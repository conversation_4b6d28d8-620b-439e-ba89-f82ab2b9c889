import streamlit as st
import json
from docx import Document
import base64
import pandas as pd
import io
from datetime import datetime
from docx.shared import RGBColor, Pt, Inches # Import Pt for font size
from docx.oxml.table import CT_Tc # Import CT_Tc for cell creation if needed
from docx.table import _Row # For accessing row._element.getparent() correctly
from docx.oxml.ns import qn # For qualified names in XML manipulation
from docx.oxml.text.paragraph import CT_P # For creating new paragraph XML elements
from docx.enum.text import WD_ALIGN_PARAGRAPH # For paragraph alignment
import os # Import os to check file existence
import re # Import re for regex in update_warranty_line
from docx.oxml import OxmlElement
from docx.oxml.ns import qn
import requests  # Add this import for OTP API
import shutil  # Add this import for file operations

# Create temporary folder for PO documents
TEMP_PO_FOLDER = "temp_po_documents"

def ensure_temp_folder_exists():
    """Create temporary folder if it doesn't exist"""
    if not os.path.exists(TEMP_PO_FOLDER):
        os.makedirs(TEMP_PO_FOLDER)

def sanitize_filename(filename):
    """Replace invalid filename characters with underscores"""
    invalid_chars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|']
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    return filename

def save_and_send_po_document(buffer, po_number, approval_stage="first"):
    """
    Save PO document to temp folder, convert to base64, send via WhatsApp, then delete
    """
    ensure_temp_folder_exists()

    # Create filename with PO number (sanitized)
    safe_po_number = sanitize_filename(po_number)
    filename = f"Purchase_Order_{safe_po_number}.docx"
    filepath = os.path.join(TEMP_PO_FOLDER, filename)

    try:
        print(f"DEBUG: Saving document to temp folder: {filepath}")

        # Save document to temp folder
        with open(filepath, "wb") as f:
            f.write(buffer.getvalue())

        print(f"DEBUG: Document saved successfully. File size: {os.path.getsize(filepath)} bytes")

        # Convert to base64
        with open(filepath, "rb") as f:
            doc_bytes = f.read()
        doc_base64 = base64.b64encode(doc_bytes).decode('utf-8')

        print(f"DEBUG: Document converted to base64. Length: {len(doc_base64)} characters")

        # Send via WhatsApp
        success = send_document_for_approval(doc_base64, po_number, approval_stage)

        # Delete file after sending (whether successful or not)
        if os.path.exists(filepath):
            os.remove(filepath)
            print(f"DEBUG: Temporary file deleted: {filepath}")

        return success

    except Exception as e:
        # Clean up file even if there's an error
        if os.path.exists(filepath):
            os.remove(filepath)
            print(f"DEBUG: Temporary file deleted due to error: {filepath}")
        raise e

# Function to convert numbers to words (simplified for demonstration)
def num_to_words(num):
    """Convert number to words with proper lakhs handling"""
    units = ["", "One", "Two", "Three", "Four", "Five", "Six", "Seven", "Eight", "Nine"]
    teens = ["Ten", "Eleven", "Twelve", "Thirteen", "Fourteen", "Fifteen",
             "Sixteen", "Seventeen", "Eighteen", "Nineteen"]
    tens = ["", "Ten", "Twenty", "Thirty", "Forty", "Fifty",
            "Sixty", "Seventy", "Eighty", "Ninety"]
    
    if num == 0:
        return "Zero"

    words = []
    # Handle negative numbers
    is_negative = False
    if num < 0:
        is_negative = True
        num = abs(num)
        words.append("Minus")

    if num >= 10000000: # Crores
        words.append(num_to_words(num // 10000000).replace(" Rupees Only", "").strip() + " Crore")
        num %= 10000000
    if num >= 100000: # Lakhs
        crore_part = num // 100000
        if crore_part > 0:
            words.append(num_to_words(crore_part).replace(" Rupees Only", "").strip() + " Lakh")
        num %= 100000
    if num >= 1000: # Thousands
        lakh_part = num // 1000
        if lakh_part > 0:
            if lakh_part < 20:
                words.append(teens[lakh_part-10] if lakh_part >= 10 else units[lakh_part])
            else:
                words.append(tens[lakh_part//10] + (" " + units[lakh_part%10] if lakh_part%10 !=0 else ""))
            words.append("Thousand")
        num %= 1000
    if num >= 1000: # Thousands
        words.append(num_to_words(num // 1000).replace(" Rupees Only", "").strip() + " Thousand")
        num %= 1000
    if num >= 100: # Hundreds
        words.append(num_to_words(num // 100).replace(" Rupees Only", "").strip() + " Hundred")
        num %= 100

    if num > 0:
        # Only add "and" if there are preceding words and the last word isn't already a connector
        if words and not any(words[-1].endswith(suffix) for suffix in ["Crore", "Lakh", "Thousand", "Hundred", "and"]):
            words.append("and")
        if num < 10:
            words.append(units[num])
        elif num < 20:
            words.append(teens[num - 10])
        else:
            words.append(tens[num // 10])
            if num % 10 > 0:
                words.append(units[num % 10])
    
    return " ".join(words).strip() + " Rupees Only"

# Helper function to process paragraphs (used for both main document and table cells)
def process_paragraph(paragraph, replacements_dict):
    # Build up the full text of the paragraph from its runs
    full_text = "".join(run.text for run in paragraph.runs)
    
    # Iterate through replacement keys to find and replace
    for key, value in replacements_dict.items():
        if key in full_text:
            new_text = full_text.replace(key, value)
            
            # Store original run formats before clearing
            original_formats = []
            for run in paragraph.runs:
                original_formats.append({
                    'bold': run.bold,
                    'italic': run.italic,
                    'underline': run.underline,
                    'font_name': run.font.name,
                    'font_size': run.font.size,
                    'color_rgb': run.font.color.rgb if run.font.color.rgb else None
                })
            
            # Clear the paragraph
            paragraph.clear()
            
            # Add the new text, trying to apply the formatting of the first run
            # or a default if no runs existed.
            new_run = paragraph.add_run(new_text)
            if original_formats:
                first_format = original_formats[0]
                new_run.bold = first_format['bold']
                new_run.italic = first_format['italic']
                new_run.underline = first_format['underline']
                if first_format['font_name']:
                    new_run.font.name = first_format['font_name']
                if first_format['font_size']:
                    new_run.font.size = first_format['font_size']
                if first_format['color_rgb']:
                    new_run.font.color.rgb = first_format['color_rgb']
            
            # Update full_text so subsequent replacements in the same paragraph work correctly
            full_text = new_text
    return full_text # Return updated full_text for subsequent replacements in the same paragraph

def update_delivery_line(document, num_days_dispatch):
    """
    Updates the specific "4. Delivery" line with the number of days from dispatch,
    keeping "4. Delivery" bold and the rest not bold.
    """
    target_start_string = "4.  Delivery"
    # A more robust way to find the specific sentence to avoid partial matches
    full_sentence_identifier = "The material shall be dispatched within 1-2 days from the date of acceptance of PO In case the material is not dispatched by the aforesaid date, the Buyer shall have the right to terminate this Purchase Order, in Which case the Seller shall promptly, and not later than 3 (three) days refund the entire Payment made by the Buyer to the Seller plus interest at the rate of 18% p.a. charged From the date of such payment. For any short supply, supplier will bear the freight charges."
    
    for paragraph in document.paragraphs:
        # Check if the paragraph text contains the identifying parts of the sentence
        if target_start_string in paragraph.text and "The material shall be dispatched within" in paragraph.text:
            full_paragraph_text = paragraph.text
            
            # Find the start of the dynamic number part "1-2 days"
            # We need to be careful with spacing, so let's find "within " and then the number
            try:
                # Find the index of "within "
                within_idx = full_paragraph_text.find("within ")
                if within_idx != -1:
                    # Search for " days" after "within " to isolate the number part
                    days_idx = full_paragraph_text.find(" days", within_idx)
                    if days_idx != -1:
                        # Extract the part that contains "1-2"
                        current_days_part = full_paragraph_text[within_idx + len("within "):days_idx]
                        
                        # Replace the old days part with the new number of days
                        new_text_content = full_paragraph_text.replace(current_days_part + " days", f"{num_days_dispatch} days")

                        paragraph.clear() # Clear all existing runs and their formatting

                        # Reconstruct the paragraph, applying bold only to "4. Delivery"
                        # Find the exact start and end of "4.  Delivery" in the new text content
                        delivery_prefix_start = new_text_content.find(target_start_string)
                        delivery_prefix_end = delivery_prefix_start + len(target_start_string)

                        # Add the content before "4.  Delivery" (if any)
                        if delivery_prefix_start > 0:
                            paragraph.add_run(new_text_content[:delivery_prefix_start]).bold = False

                        # Add "4.  Delivery" (bold)
                        delivery_run = paragraph.add_run(new_text_content[delivery_prefix_start:delivery_prefix_end])
                        delivery_run.bold = True

                        # Add the rest of the content (not bold)
                        paragraph.add_run(new_text_content[delivery_prefix_end:]).bold = False
                        
                        return # Exit after updating this specific paragraph
            except Exception as e:
                st.warning(f"Error updating delivery line: {e}")
                # Fallback to general replacement if specific parsing fails
                pass # Continue to general replacement if specific parsing fails

def update_warranty_line(document, num_years_warranty):
    """
    Updates the specific '6.  Warranty' line with the number of years from input,
    keeping '6.  Warranty' bold and the rest not bold.
    """
    target_start_string = "6.  Warranty"
    for paragraph in document.paragraphs:
        if target_start_string in paragraph.text and "year(s) from the date of invoice" in paragraph.text:
            full_paragraph_text = paragraph.text
            # Find the current number of years (e.g., '1 year(s)')
            match = re.search(r"(\d+) year\(s\)", full_paragraph_text)
            if match:
                current_years_part = match.group(1)
                new_text_content = full_paragraph_text.replace(f"{current_years_part} year(s)", f"{num_years_warranty} year(s)")
                paragraph.clear()
                warranty_prefix_start = new_text_content.find(target_start_string)
                warranty_prefix_end = warranty_prefix_start + len(target_start_string)
                if warranty_prefix_start > 0:
                    paragraph.add_run(new_text_content[:warranty_prefix_start]).bold = False
                warranty_run = paragraph.add_run(new_text_content[warranty_prefix_start:warranty_prefix_end])
                warranty_run.bold = True
                paragraph.add_run(new_text_content[warranty_prefix_end:]).bold = False
                return

def update_transit_insurance_line(document, transit_insurance_scope):
    """
    Updates the specific '7. Transit Insurance' line with the selected scope (Vendor or MRL),
    keeping '7. Transit Insurance' bold and the rest not bold.
    """
    target_start_string = "7. Transit Insurance"
    for paragraph in document.paragraphs:
        if target_start_string in paragraph.text and "Transit Insurance is in" in paragraph.text:
            full_paragraph_text = paragraph.text
            # Replace the scope in the sentence
            new_scope = transit_insurance_scope
            new_text_content = re.sub(r"Transit Insurance is in [A-Za-z]+ scope\.", f"Transit Insurance is in {new_scope} scope.", full_paragraph_text)
            paragraph.clear()
            insurance_prefix_start = new_text_content.find(target_start_string)
            insurance_prefix_end = insurance_prefix_start + len(target_start_string)
            if insurance_prefix_start > 0:
                paragraph.add_run(new_text_content[:insurance_prefix_start]).bold = False
            insurance_run = paragraph.add_run(new_text_content[insurance_prefix_start:insurance_prefix_end])
            insurance_run.bold = True
            paragraph.add_run(new_text_content[insurance_prefix_end:]).bold = False
            return

def update_payment_terms_line(document, payment_terms_text):
    """
    Updates the specific '2. Payment Terms' line with the selected payment terms text,
    keeping '2. Payment Terms' bold and the rest not bold.
    """
    target_start_string = "2. Payment Terms"
    for paragraph in document.paragraphs:
        if target_start_string in paragraph.text and "advance payment after acceptance PO & receipt of the PI" in paragraph.text:
            full_paragraph_text = paragraph.text
            import re
            # Replace the old payment terms with the new one
            new_text_content = re.sub(r"100 % advance payment after acceptance PO & receipt of the PI\." , payment_terms_text, full_paragraph_text)
            paragraph.clear()
            payment_prefix_start = new_text_content.find(target_start_string)
            payment_prefix_end = payment_prefix_start + len(target_start_string)
            if payment_prefix_start > 0:
                paragraph.add_run(new_text_content[:payment_prefix_start]).bold = False
            payment_run = paragraph.add_run(new_text_content[payment_prefix_start:payment_prefix_end])
            payment_run.bold = True
            paragraph.add_run(new_text_content[payment_prefix_end:]).bold = False
            return

def set_table_borders(table):
    """
    Set visible borders for all cells in the given table.
    """
    tbl = table._element
    tblPr = tbl.tblPr
    tblBorders = OxmlElement('w:tblBorders')
    for border_name in ('top', 'left', 'bottom', 'right', 'insideH', 'insideV'):
        border = OxmlElement(f'w:{border_name}')
        border.set(qn('w:val'), 'single')
        border.set(qn('w:sz'), '8')  # 8 eighths of a point = 1pt
        border.set(qn('w:space'), '0')
        border.set(qn('w:color'), '000000')
        tblBorders.append(border)
    tblPr.append(tblBorders)

def replace_text_in_docx(doc_stream, replacements, purchase_items_df=None, total_amount_details=None, delivery_address_to_insert=None, project_name_from_db=None, inco_term_selected=None, freight_amount=None, freight_gst_percent=None, num_days_dispatch=None, num_years_warranty=None, transit_insurance_scope=None, payment_terms_text=None):
    # Update price basis text based on inco term (normal font)
    if inco_term_selected == "FOR":
        replacements["The above prices are ex showroom/factory"] = "The above prices are FOR (Free on Road)"  # Not bold
    elif inco_term_selected in ["Ex works", "CIF", "CPT"]:
        replacements["The above prices are ex showroom/factory"] = "The above prices are " + inco_term_selected
    """
    Replaces text in a Word document based on a dictionary of replacements,
    and replaces a specific table with data from purchase_items_df.
    """
    document = Document(doc_stream)

    # Specific handling for the "Taxes" line
    original_taxes_key_in_replacements = "GST @18% & 12%"
    dynamic_gst_text_for_taxes = replacements.get(original_taxes_key_in_replacements, None)

    # Handle Delivery line update
    if num_days_dispatch is not None:
        update_delivery_line(document, num_days_dispatch)
        # Remove the original delivery line from generic replacements if it exists
        original_delivery_line = "4.  Delivery              :             The material shall be dispatched within 1-2 days from the date of acceptance of PO In case the material is not dispatched by the aforesaid date, the Buyer shall have the right to terminate this Purchase Order, in Which case the Seller shall promptly, and not later than 3 (three) days refund the entire Payment made by the Buyer to the Seller plus interest at the rate of 18% p.a. charged From the date of such payment. For any short supply, supplier will bear the freight charges."
        if original_delivery_line in replacements:
            del replacements[original_delivery_line]

    # Warranty line update
    if num_years_warranty is not None:
        update_warranty_line(document, num_years_warranty)

    # Transit Insurance line update
    if transit_insurance_scope is not None:
        update_transit_insurance_line(document, transit_insurance_scope)

    if payment_terms_text is not None:
        update_payment_terms_line(document, payment_terms_text)


    # Hardcoded old address string to find and replace
    old_address_to_find = """Village: - Fateu Near Sahiya market Samalta,
Block: Kalsi, District: Dehradun, State: 
Uttarakhand, Pin-Code 248196"""
    # Normalize line endings for comparison
    old_address_to_find_normalized = old_address_to_find.replace('\r\n', '\n').replace('\r', '\n')

    # Remove the old address from the general replacements dictionary if it exists
    # This prevents it from being processed by process_paragraph later
    if old_address_to_find in replacements:
        del replacements[old_address_to_find]


    # Handle Delivery Address replacement specifically within tables
    if delivery_address_to_insert:
        delivery_address_cell_found = False
        target_cell_for_address = None

        for table in document.tables:
            for row in table.rows:
                for cell in row.cells:
                    # Check if "Delivery Address:" is in the current cell's text
                    cell_full_text_normalized = cell.text.replace('\r\n', '\n').replace('\r', '\n')

                    if "Delivery Address:" in cell_full_text_normalized:
                        target_cell_for_address = cell # The address is in THIS cell
                        delivery_address_cell_found = True
                        break # Found the cell, exit inner loop
                if delivery_address_cell_found:
                    break # Found the row, exit outer loop
            if delivery_address_cell_found:
                break # Found the table, exit outermost loop
        
        if target_cell_for_address:
            current_cell_text = target_cell_for_address.text.replace('\r\n', '\n').replace('\r', '\n')
            
            # Find the start of the old address block
            start_idx_old_address = current_cell_text.find(old_address_to_find_normalized)

            if start_idx_old_address != -1:
                # Construct the new content: part before old address + new address
                new_cell_content = current_cell_text[:start_idx_old_address] + delivery_address_to_insert
                
                # Clear all existing paragraphs in the cell
                for p in target_cell_for_address.paragraphs:
                    p.clear()
                    target_cell_for_address._element.remove(p._element) # Remove the paragraph element
                
                # Add the modified content back into the cell as a single paragraph
                # This will render internal \n as line breaks
                target_cell_for_address.add_paragraph(new_cell_content)
            else:
                st.warning(f"Found 'Delivery Address:' label but could not find the specific old address block starting with '{old_address_to_find.splitlines()[0]}...' within that cell. Address not replaced.")
        else:
            st.warning("Could not find 'Delivery Address:' in a table in the Word document. Please ensure this label exists in a table cell.")


    # Apply general text replacements in paragraphs
    for paragraph in document.paragraphs:
        process_paragraph(paragraph, replacements)

    # Apply general text replacements in tables (cells)
    for table in document.tables:
        for row in table.rows:
            for cell in row.cells:
                for paragraph in cell.paragraphs:
                    process_paragraph(paragraph, replacements)

    # --- Bolding the Project Name specifically ---
    if project_name_from_db: # This is the actual project name string from the DB
        # Iterate through paragraphs
        for paragraph in document.paragraphs:
            if project_name_from_db in paragraph.text:
                for run in paragraph.runs:
                    # Check if the run's text contains the project name
                    if project_name_from_db in run.text:
                        run.bold = True
        
        # Iterate through table cells
        for table in document.tables:
            for row in table.rows:
                for cell in row.cells:
                    # Check if the cell's combined text contains the project name
                    if project_name_from_db in cell.text:
                        for paragraph in cell.paragraphs:
                            if project_name_from_db in paragraph.text:
                                for run in paragraph.runs:
                                    # Check if the run's text contains the project name
                                    if project_name_from_db in run.text:
                                        run.bold = True

    # --- Table Replacement Logic for Purchase Items ---
    if purchase_items_df is not None:
        target_table = None
        # Find the table that contains "S.NO" in its first row (header)
        for table in document.tables:
            if len(table.rows) > 0 and len(table.rows[0].cells) > 0:
                header_text = table.rows[0].cells[0].text.strip()
                if header_text == "S.NO": # Assuming "S.NO" is in the first cell of the header row
                    target_table = table
                    break
        has_cgst_sgst = "CGST" in purchase_items_df.columns and "SGST" in purchase_items_df.columns
        if has_cgst_sgst:
            expected_cols_for_items = 10
            item_headers = ["S.NO", "Description of Item", "Qty", "UOM", "Unit Price", "Taxable Amount (INR)", "CGST", "SGST", "GST Amount (INR)", "Total Amount (INR)"]
        else:
            expected_cols_for_items = 9
            item_headers = ["S.NO", "Description of Item", "Qty", "UOM", "Unit Price", "Taxable Amount (INR)", "GST", "GST Amount (INR)", "Total Amount (INR)"]
        if target_table:
            # If the template table does not have enough columns, create a new table and replace it
            if len(target_table.rows[0].cells) < expected_cols_for_items:
                # Create a new table with the correct number of columns
                parent = target_table._element.getparent()
                idx = list(parent).index(target_table._element)
                new_table = target_table._parent.add_table(rows=1, cols=expected_cols_for_items, width=Inches(6.0))
                new_table.style = target_table.style
                # Copy header
                for i, header in enumerate(item_headers):
                    new_table.rows[0].cells[i].text = header
                # Bold the header row
                for cell in new_table.rows[0].cells:
                    for paragraph in cell.paragraphs:
                        for run in paragraph.runs:
                            run.bold = True
                set_table_borders(new_table)
                # Remove the old table and insert the new one
                parent.remove(target_table._element)
                parent.insert(idx, new_table._element)
                target_table = new_table
            else:
                # Bold the header row of the existing table
                for cell in target_table.rows[0].cells:
                    for paragraph in cell.paragraphs:
                        for run in paragraph.runs:
                            run.bold = True
                set_table_borders(target_table)
            # Remove all rows except the header
            for i in range(len(target_table.rows) - 1, 0, -1):
                target_table._element.remove(target_table.rows[i]._element)
            # Add new rows from purchase_items_df right after the header row
            for index, row_data in purchase_items_df.iterrows():
                row = target_table.add_row().cells
                # Populate the cells with data from the DataFrame
                row[0].text = str(row_data.get("S.NO", ""))
                row[1].text = str(row_data.get("Description of Item", ""))
                row[2].text = str(row_data.get("Qty", ""))
                row[3].text = str(row_data.get("UOM", ""))
                row[4].text = f"{row_data.get('Unit Price', 0.0):,.2f}"
                row[5].text = f"{row_data.get('Taxable Amount (INR)', 0.0):,.2f}"
                if has_cgst_sgst:
                    cgst_value = row_data.get("CGST", 0)
                    if pd.notna(cgst_value) and cgst_value != "":
                        try:
                            row[6].text = f"{float(cgst_value) * 100:.0f}%"
                        except ValueError:
                            row[6].text = str(cgst_value)
                    else:
                        row[6].text = "0%"
                    sgst_value = row_data.get("SGST", 0)
                    if pd.notna(sgst_value) and sgst_value != "":
                        try:
                            row[7].text = f"{float(sgst_value) * 100:.0f}%"
                        except ValueError:
                            row[7].text = str(sgst_value)
                    else:
                        row[7].text = "0%"
                    taxable = row_data.get('Taxable Amount (INR)', 0.0)
                    cgst = float(row_data.get("CGST", 0) or 0)
                    sgst = float(row_data.get("SGST", 0) or 0)
                    gst_amount = taxable * (cgst + sgst)
                    row[8].text = f"{gst_amount:,.2f}"
                    row[9].text = f"{row_data.get('Total Amount (INR)', 0.0):,.2f}"
                else:
                    gst_value = row_data.get("GST", 0)
                    if pd.notna(gst_value) and gst_value != "":
                        try:
                            row[6].text = f"{float(gst_value) * 100:.0f}%"
                        except ValueError:
                            row[6].text = str(gst_value)
                    else:
                        row[6].text = "0%"
                    row[7].text = f"{row_data.get('GST Amount (INR)', 0.0):,.2f}"
                    row[8].text = f"{row_data.get('Total Amount (INR)', 0.0):,.2f}"
            # --- Add Freight Charges as a new item row if applicable ---
            if inco_term_selected in ["Ex works", "CIF", "CPT"] and freight_amount is not None and freight_amount > 0:
                freight_taxable_amount = float(freight_amount)
                if has_cgst_sgst:
                    cgst = freight_gst_percent/2 if freight_gst_percent else 0.0
                    sgst = freight_gst_percent/2 if freight_gst_percent else 0.0
                    gst_amount = freight_taxable_amount * (cgst + sgst)
                    total_amount = freight_taxable_amount + gst_amount
                    freight_row = target_table.add_row().cells
                    freight_row[0].text = str(len(purchase_items_df) + 1)
                    freight_row[1].text = "Freight Charges"
                    freight_row[2].text = "1"
                    freight_row[3].text = "Nos"
                    freight_row[4].text = f"{freight_amount:,.2f}"
                    freight_row[5].text = f"{freight_taxable_amount:,.2f}"
                    freight_row[6].text = f"{cgst*100:.0f}%"
                    freight_row[7].text = f"{sgst*100:.0f}%"
                    freight_row[8].text = f"{gst_amount:,.2f}"
                    freight_row[9].text = f"{total_amount:,.2f}"
                    total_amount_details["total_basic_amount"] += freight_taxable_amount
                    total_amount_details["gst_amount"] += gst_amount
                    total_amount_details["grand_total"] += total_amount
                    total_amount_details["amount_in_words"] = num_to_words(int(total_amount_details["grand_total"]))
                else:
                    freight_gst_amount = (freight_taxable_amount * freight_gst_percent) if freight_gst_percent else 0.0
                    freight_total_amount = freight_taxable_amount + freight_gst_amount
                    freight_row = target_table.add_row().cells
                    freight_row[0].text = str(len(purchase_items_df) + 1)
                    freight_row[1].text = "Freight Charges"
                    freight_row[2].text = "1"
                    freight_row[3].text = "Nos"
                    freight_row[4].text = f"{freight_amount:,.2f}"
                    freight_row[5].text = f"{freight_taxable_amount:,.2f}"
                    freight_row[6].text = f"{freight_gst_percent * 100:.0f}%" if freight_gst_percent is not None else "0%"
                    freight_row[7].text = f"{freight_gst_amount:,.2f}"
                    freight_row[8].text = f"{freight_total_amount:,.2f}"
                    total_amount_details["total_basic_amount"] += freight_taxable_amount
                    total_amount_details["gst_amount"] += freight_gst_amount
                    total_amount_details["grand_total"] += freight_total_amount
                    total_amount_details["amount_in_words"] = num_to_words(int(total_amount_details["grand_total"]))
            # --- Dynamically add Total Taxable Amount (INR) and Grand Total with GST rows ---
            total_taxable_row = target_table.add_row().cells 
            if len(total_taxable_row) >= expected_cols_for_items: 
                for i in range(1, expected_cols_for_items - 1): 
                    total_taxable_row[0].merge(total_taxable_row[i])
                if not total_taxable_row[0].paragraphs:
                    total_taxable_row[0].add_paragraph()
                merged_label_paragraph = total_taxable_row[0].paragraphs[0]
                merged_label_paragraph.text = "Total Taxable Amount (INR)" 
                for run in merged_label_paragraph.runs:
                    run.bold = True
                if not total_taxable_row[-1].paragraphs:
                    total_taxable_row[-1].add_paragraph()
                value_paragraph = total_taxable_row[-1].paragraphs[0]
                value_paragraph.text = f"{total_amount_details['total_basic_amount']:,.2f}"
                for run in value_paragraph.runs:
                    run.bold = True
            else:
                st.warning("Not enough columns to properly display 'Total Taxable Amount (INR)' row.")
            grand_total_row = target_table.add_row().cells
            if len(grand_total_row) >= expected_cols_for_items:
                for i in range(1, expected_cols_for_items - 1): 
                    grand_total_row[0].merge(grand_total_row[i])
                if not grand_total_row[0].paragraphs:
                    grand_total_row[0].add_paragraph()
                merged_label_paragraph = grand_total_row[0].paragraphs[0]
                merged_label_paragraph.text = "Grand Total with GST"
                for run in merged_label_paragraph.runs:
                    run.bold = True
                if not grand_total_row[-1].paragraphs:
                    grand_total_row[-1].add_paragraph()
                value_paragraph = grand_total_row[-1].paragraphs[0]
                value_paragraph.text = f"{total_amount_details['grand_total']:,.2f}"
                for run in value_paragraph.runs:
                    run.bold = True
            else:
                st.warning("Not enough columns to properly display 'Grand Total with GST' row.")
            amount_in_word_row = target_table.add_row().cells
            if len(amount_in_word_row) >= expected_cols_for_items:
                first_cell = amount_in_word_row[0]
                for i in range(1, expected_cols_for_items):
                    first_cell.merge(amount_in_word_row[i])
                if not first_cell.paragraphs:
                    first_cell.add_paragraph()
                merged_paragraph = first_cell.paragraphs[0]
                amount_in_words_text = total_amount_details['amount_in_words'].replace("(", "").replace(")", "")
                merged_paragraph.text = f"Amount in word :  - {amount_in_words_text}"
                merged_paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
                for run in merged_paragraph.runs:
                    run.bold = True
            else:
                if not amount_in_word_row[0].paragraphs:
                    amount_in_word_row[0].add_paragraph()
                amount_in_word_row[0].paragraphs[0].text = f"Amount in word :  - {total_amount_details['amount_in_words'].replace('(', '').replace(')', '')}"
                amount_in_word_row[0].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.LEFT
                for run in amount_in_word_row[0].paragraphs[0].runs:
                    run.bold = True
        else:
            st.warning("Could not find the main items table in the Word document. Please ensure it has a header like 'S.NO' in its first cell.")
    return document

def main():
    st.set_page_config(layout="wide")
    st.title("Word Document Automation from Excel")
    st.markdown("""
    Upload the Purchase Items Excel file and enter the Subject.
    The application will use pre-defined Vendor Details, Project DB, and Word Template files
    from the same directory to generate the customized Word document.
    """)

    # Define paths for directly fetched files
    vendor_db_path = "Vendor DB.xlsx"
    project_db_path = "Project DB.xlsx"
    word_template_path = "Purchase of Electrical Items.docx"
    po_db_path = "PO DB.xlsx" # Path for PO database

    # Define Dwarka Office Address
    dwarka_office_address = """3rd Floor, D-21, Corporate Park, Sector 21,
Dwarka South West Delhi , 
New Delhi - 110077"""

    # Load pre-defined files
    vendor_df = None
    project_db_df = None
    word_template_bytes = None
    po_df = None

    try:
        vendor_df = pd.read_excel(vendor_db_path)
        project_db_df = pd.read_excel(project_db_path)
        
        # Read Word template as bytes
        with open(word_template_path, "rb") as f:
            word_template_bytes = f.read()
        
        # Load PO DB or create if not exists
        if os.path.exists(po_db_path):
            po_df = pd.read_excel(po_db_path)
            # Ensure required columns exist in po_df, add if missing
            required_po_cols = ["PO No.", "Vendor Name", "Project Name", "Subject", "Delivery Address Type"] # Added Delivery Address Type
            for col in required_po_cols:
                if col not in po_df.columns:
                    po_df[col] = None # Add missing column with None values
        else:
            po_df = pd.DataFrame(columns=["PO No.", "Vendor Name", "Project Name", "Subject", "Delivery Address Type"]) # Added Delivery Address Type
            po_df.to_excel(po_db_path, index=False) # Create the file with new columns

        st.success("Pre-defined files (Vendor DB, Project DB, Word Template, PO DB) loaded successfully!")

    except FileNotFoundError as e:
        st.error(f"Error: Required file not found. Please ensure '{e.filename}' is in the same directory as main.py.")
        return
    except Exception as e:
        st.error(f"An error occurred while loading pre-defined files: {e}")
        return

    # Sidebar for mode selection
    st.sidebar.header("Mode Selection")
    app_mode = st.sidebar.radio(
        "Choose the application mode:",
        ("Create New PO", "Edit Existing PO")
    )

    # Main content area based on mode
    if app_mode == "Create New PO":
        st.header("Create New Purchase Order")

        subject_input = st.text_input("Enter Subject for the Purchase Order:", "Purchase of Electrical Items- for 33kv Transmission Line")

        # Use columns for horizontal layout
        col1, col2, col3 = st.columns(3)

        with col1:
            vendor_names = ["Select a Vendor"] + vendor_df["Name"].tolist()
            selected_vendor_name = st.selectbox("Select Vendor:", vendor_names, key="new_po_vendor_select")

        with col2:
            project_names = ["Select a Project"] + project_db_df["Name"].tolist()
            selected_project_name = st.selectbox("Select Project:", project_names, key="new_po_project_select")

        with col3:
            delivery_address_options = ["Project Address", "Dwarka Office"]
            selected_delivery_address_type = st.selectbox("Select Delivery Address Type:", delivery_address_options, key="new_po_delivery_address_type")

        # Inco Term, Freight Charges, GST for Freight Charges in one row
        col_inco, col_freight, col_gst = st.columns(3)
        with col_inco:
            inco_term_options = ["FOR", "Ex works", "CIF", "CPT"]
            selected_inco_term = st.selectbox("Select Inco Term:", inco_term_options, key="new_po_inco_term")
        with col_freight:
            freight_charges_amount = None
            if selected_inco_term in ["Ex works", "CIF", "CPT"]:
                freight_charges_amount = st.number_input("Enter Freight Charges Amount (INR):", min_value=0.0, value=0.0, key="new_po_freight_amount")
        with col_gst:
            freight_gst_percent = None
            if selected_inco_term in ["Ex works", "CIF", "CPT"]:
                gst_options = ["0%", "12%", "18%"]
                selected_freight_gst = st.selectbox("Select GST for Freight Charges:", gst_options, key="new_po_freight_gst")
                freight_gst_percent = float(selected_freight_gst.replace('%', '')) / 100.0
            else:
                freight_gst_percent = None

        # Number of Days for Dispatch, Warranty Period, Transit Insurance Scope in one row
        col_days, col_warranty, col_insurance = st.columns(3)
        with col_days:
            num_days_dispatch = st.number_input("Enter Number of Days for Dispatch:", min_value=1, value=1, key="new_po_num_days_dispatch")
        with col_warranty:
            num_years_warranty = st.number_input("Enter Warranty Period (years):", min_value=1, value=1, key="new_po_num_years_warranty")
        with col_insurance:
            transit_insurance_options = ["Vendor", "MRL"]
            transit_insurance_scope = st.selectbox("Select Transit Insurance Scope:", transit_insurance_options, key="new_po_transit_insurance_scope")

        payment_terms_options = [
            "10% advance on PI with PO, balance 90% on dispatch of material with GST.",
            "20% advance on PI with PO, balance 80% on dispatch of material with GST.",
            "30% advance on PI with PO, balance 70% on dispatch of material with GST.",
            "50% advance on PI with PO, balance 50% on dispatch of material with GST.",
            "100% payment before dispatch of material.",
            "100% payment after delivery of material.",
            "30 days credit payment.",
            "15 days credit payment.",
            "10% advance on PI, 90% balance on Letter of credit for 30 days.",
            "100% payment on Letter of credit (Irrevocable LC) for 30 days",
            "50% advance on PI, 50% balance on Letter of credit for 30 days.",
            "10% advance on PI, 70% balance against the submission of RA bills, 20% after handover of site.",
            "10% advance on PI, 80% balance against the submission of RA bills, 10% after handover of site.",
            "10% advance on PI, 80% balance before dispatch of material, 10% against submission of PBG till warranty period."
        ]
        payment_terms_text = st.selectbox("Select Payment Terms:", payment_terms_options, key="new_po_payment_terms_text")

        purchase_items_excel_file = st.file_uploader("Upload Purchase Items Excel File (CSV, XLSX)", type=["csv", "xlsx"], key="new_po_items_excel")

        # Conditional logic for processing
        if selected_vendor_name == "Select a Vendor" or selected_project_name == "Select a Project" or purchase_items_excel_file is None:
            st.info("Please select a Vendor, a Project, and upload the Purchase Items Excel file to proceed.")
            return

        # Filter dataframes based on selection
        selected_vendor_row = vendor_df[vendor_df["Name"] == selected_vendor_name].iloc[0]
        selected_project_row = project_db_df[project_db_df["Name"] == selected_project_name].iloc[0]

        # --- PO Number Pre-calculation for display ---
        current_date_str = datetime.now().strftime("%d%m%Y")
        project_code = str(selected_project_row["Code"]).strip() if pd.notna(selected_project_row["Code"]) else "UNKNOWN_CODE"

        # Filter POs for the current date and project code
        po_df["PO No."] = po_df["PO No."].astype(str) 
        project_specific_pos = po_df[po_df["PO No."].str.contains(f"MRL/{project_code}/", na=False)]
        daily_project_pos = project_specific_pos[po_df["PO No."].str.contains(f"/{current_date_str}/", na=False)]

        next_incremental_num = 1
        if not daily_project_pos.empty:
            last_po_numbers = daily_project_pos["PO No."].str.extract(r'/(\d+)$').astype(float).fillna(0).iloc[:, 0]
            next_incremental_num = int(last_po_numbers.max()) + 1
        
        formatted_incremental_num = f"{next_incremental_num:03d}" 
        current_session_po_number = f"MRL/{project_code}/{current_date_str}/{formatted_incremental_num}"
        st.info(f"The next PO Number will be: **{current_session_po_number}**")

        # --- "Generate & Download PO" button ---
        if st.button("Generate & Download Purchase Order"):
            try:
                # Read Purchase Items Excel data
                if purchase_items_excel_file.name.endswith('.csv'):
                    purchase_items_df = pd.read_csv(purchase_items_excel_file) 
                else:
                    purchase_items_df = pd.read_excel(purchase_items_excel_file)
                
                # Validation checks (as before)
                required_vendor_columns = ["Name", "Address", "GST No", "Contact Person", "Contact No 1", "Contact No 2", "Email"]
                if not all(col in vendor_df.columns for col in required_vendor_columns):
                    st.error(f"Vendor Details Excel file must contain all of these columns: {', '.join(required_vendor_columns)}")
                    return

                required_gst = ["S.NO", "Description of Item", "Qty", "UOM", "Unit Price", "Taxable Amount (INR)", "GST", "GST Amount (INR)", "Total Amount (INR)"]
                required_cgst_sgst = ["S.NO", "Description of Item", "Qty", "UOM", "Unit Price", "Taxable Amount (INR)", "CGST", "SGST", "GST Amount (INR)", "Total Amount (INR)"]
                if not (all(col in purchase_items_df.columns for col in required_gst) or all(col in purchase_items_df.columns for col in required_cgst_sgst)):
                    st.error(
                        "Purchase Items Excel file must contain either:\n"
                        f"{', '.join(required_gst)}\n"
                        "OR\n"
                        f"{', '.join(required_cgst_sgst)}"
                    )
                    return

                required_project_columns = ["Name", "Address", "GST No", "Code"] 
                if not all(col in project_db_df.columns for col in required_project_columns):
                    st.error(f"Project DB Excel file must contain all of these columns: {', '.join(required_project_columns)}. Missing: {', '.join(set(required_project_columns) - set(project_db_df.columns))}")
                    return

                # Ensure relevant columns in purchase_items_df are numeric for processing
                purchase_items_df["Taxable Amount (INR)"] = pd.to_numeric(purchase_items_df["Taxable Amount (INR)"], errors='coerce').fillna(0)
                purchase_items_df["GST Amount (INR)"] = pd.to_numeric(purchase_items_df["GST Amount (INR)"], errors='coerce').fillna(0)
                purchase_items_df["Total Amount (INR)"] = pd.to_numeric(purchase_items_df["Total Amount (INR)"], errors='coerce').fillna(0)
                if "GST" in purchase_items_df.columns:
                    purchase_items_df["GST"] = pd.to_numeric(purchase_items_df["GST"], errors='coerce').fillna(0)
                if "CGST" in purchase_items_df.columns:
                    purchase_items_df["CGST"] = pd.to_numeric(purchase_items_df["CGST"], errors='coerce').fillna(0)
                if "SGST" in purchase_items_df.columns:
                    purchase_items_df["SGST"] = pd.to_numeric(purchase_items_df["SGST"], errors='coerce').fillna(0)

                # Use selected vendor and project row data
                vendor_name = str(selected_vendor_row["Name"]).strip() if pd.notna(selected_vendor_row["Name"]) else ""
                vendor_address = str(selected_vendor_row["Address"]).strip() if pd.notna(selected_vendor_row["Address"]) else ""
                vendor_gst = str(selected_vendor_row["GST No"]).strip() if pd.notna(selected_vendor_row["GST No"]) else ""
                contact_person = str(selected_vendor_row["Contact Person"]).strip() if pd.notna(selected_vendor_row["Contact Person"]) else ""
                contact_no1 = str(selected_vendor_row["Contact No 1"]).strip() if pd.notna(selected_vendor_row["Contact No 1"]) else ""
                contact_no2 = str(selected_vendor_row["Contact No 2"]).strip() if pd.notna(selected_vendor_row["Contact No 2"]) else ""
                vendor_email = str(selected_vendor_row["Email"]).strip() if pd.notna(selected_vendor_row["Email"]) else ""

                combined_contact_no = ""
                if contact_no1 and contact_no2:
                    combined_contact_no = f"{contact_no1}, {contact_no2}"
                elif contact_no1:
                    combined_contact_no = contact_no1
                elif contact_no2:
                    combined_contact_no = contact_no2

                project_name = str(selected_project_row["Name"]).strip() if pd.notna(selected_project_row["Name"]) else ""
                project_address = str(selected_project_row["Address"]).strip() if pd.notna(selected_project_row["Address"]) else ""
                project_gst = str(selected_project_row["GST No"]).strip() if pd.notna(selected_project_row["GST No"]) else ""
                # project_code is already determined above for PO number generation

                # Determine the actual delivery address based on selection
                final_delivery_address = ""
                if selected_delivery_address_type == "Project Address":
                    final_delivery_address = project_address
                elif selected_delivery_address_type == "Dwarka Office":
                    final_delivery_address = dwarka_office_address

                # Calculate totals from Purchase Items DataFrame using new column names
                total_basic_amount = purchase_items_df["Taxable Amount (INR)"].sum()
                grand_total = purchase_items_df["Total Amount (INR)"].sum()
                gst_amount = purchase_items_df["GST Amount (INR)"].sum()
                
                amount_in_words = num_to_words(int(grand_total)) 

                total_amount_details = {
                    "total_basic_amount": total_basic_amount,
                    "gst_amount": gst_amount,
                    "grand_total": grand_total,
                    "amount_in_words": amount_in_words
                }

                # Determine GST percentage text for replacement
                if "GST" in purchase_items_df.columns:
                    unique_gst_values = purchase_items_df["GST"].unique()
                    present_gst_percentages = set()
                    for gst_val in unique_gst_values:
                        if pd.notna(gst_val) and gst_val != 0:
                            try:
                                percent_val = int(float(gst_val) * 100)
                                if percent_val == 18:
                                    present_gst_percentages.add(18)
                                elif percent_val == 12:
                                    present_gst_percentages.add(12)
                            except ValueError:
                                pass
                    gst_display_parts = []
                    if 18 in present_gst_percentages:
                        gst_display_parts.append("@18%")
                    if 12 in present_gst_percentages:
                        gst_display_parts.append("@12%")
                    gst_display_parts.sort(key=lambda x: int(x.replace('@', '').replace('%', '')))
                    if len(gst_display_parts) == 2:
                        gst_text_replacement = "GST " + " & ".join(gst_display_parts)
                    elif len(gst_display_parts) == 1:
                        gst_text_replacement = "GST " + gst_display_parts[0]
                    else:
                        gst_text_replacement = "GST"
                elif "CGST" in purchase_items_df.columns and "SGST" in purchase_items_df.columns:
                    cgst_vals = purchase_items_df["CGST"].unique()
                    sgst_vals = purchase_items_df["SGST"].unique()
                    cgst_percent = set()
                    sgst_percent = set()
                    for val in cgst_vals:
                        if pd.notna(val) and val != 0:
                            try:
                                cgst_percent.add(int(float(val) * 100))
                            except ValueError:
                                pass
                    for val in sgst_vals:
                        if pd.notna(val) and val != 0:
                            try:
                                sgst_percent.add(int(float(val) * 100))
                            except ValueError:
                                pass
                    cgst_str = ', '.join([f'@{v}% CGST' for v in sorted(cgst_percent)])
                    sgst_str = ', '.join([f'@{v}% SGST' for v in sorted(sgst_percent)])
                    if cgst_str and sgst_str:
                        gst_text_replacement = f'GST {cgst_str} & {sgst_str}'
                    elif cgst_str:
                        gst_text_replacement = f'GST {cgst_str}'
                    elif sgst_str:
                        gst_text_replacement = f'GST {sgst_str}'
                    else:
                        gst_text_replacement = 'GST'
                else:
                    gst_text_replacement = 'GST'

                # Use the pre-calculated PO Number for this session
                new_po_number_for_doc = current_session_po_number

                # Update PO DB with the new PO number (only when button is pressed)
                new_po_entry = pd.DataFrame([{
                    "PO No.": new_po_number_for_doc,
                    "Vendor Name": selected_vendor_name,
                    "Project Name": selected_project_name,
                    "Subject": subject_input,
                    "Delivery Address Type": selected_delivery_address_type, # Save selected address type
                    "Num Days Dispatch": num_days_dispatch, # Save number of days for dispatch
                    "Num Years Warranty": num_years_warranty, # Save number of years for warranty
                    "Transit Insurance Scope": transit_insurance_scope, # Save transit insurance scope
                    "Payment Terms Text": payment_terms_text, # Save payment terms text
                    "Created At": datetime.now().strftime("%Y-%m-%d %H:%M:%S") # Add creation timestamp
                }])
                po_df = pd.concat([po_df, new_po_entry], ignore_index=True)
                po_df.to_excel(po_db_path, index=False)
                st.success(f"PO Number recorded in PO DB: {new_po_number_for_doc}")

                # Define replacements for the current vendor and project
                replacements = {
                    "M/s Goyal Electric Works": f"M/s {vendor_name}",
                    "1541/D34, 1st floor, Krishna Market, Bhagirath Palace, Delhi 110006": vendor_address,
                    "07AALPJ3113A1ZD": vendor_gst,
                    "JYOTI RAGHAV": contact_person,
                    "8368554622": combined_contact_no,
                    "<EMAIL>": vendor_email,
                    "27-06-2025": datetime.now().strftime("%d-%m-%Y"), 
                    "Purchase of Electrical Items- for 33kv Transmission Line": subject_input,
                    "GST @18% & 12%": gst_text_replacement, 
                    
                    # Project DB replacements - these are now handled specifically in replace_text_in_docx
                    "Evincea Renewables (Three) Pvt Ltd  - 2.5 MW (TVS)": project_name,
                    "05AAHCE8670N1Z2": project_gst,
                    "MRL/ERPL3/27062025/064": new_po_number_for_doc,
                    "Purchase Order": "Purchase Order" # Ensure this is explicitly set for new POs
                }

                # Apply replacements and table update
                modified_doc = replace_text_in_docx(
                    io.BytesIO(word_template_bytes), 
                    replacements,
                    purchase_items_df,
                    total_amount_details,
                    final_delivery_address, # Pass the determined delivery address
                    project_name, # Pass the project name from DB
                    selected_inco_term, # Pass selected Inco Term
                    freight_charges_amount, # Pass freight charges amount
                    freight_gst_percent, # Pass freight GST percentage
                    num_days_dispatch, # Pass number of days for dispatch
                    num_years_warranty, # Pass number of years for warranty
                    transit_insurance_scope, # Pass transit insurance scope
                    payment_terms_text # Pass payment terms text
                )

                # Save the modified document to a BytesIO object
                buffer = io.BytesIO()
                modified_doc.save(buffer)
                buffer.seek(0)
                
                st.subheader("Download Modified Word Document:")
                # Save document locally for download and approval system
                po_filename = f"Purchase Order - {vendor_name}.docx"
                # Use sanitized filename for approval system
                safe_po_number = sanitize_filename(new_po_number_for_doc)
                po_filename_for_approval = f"Purchase Order - {safe_po_number}.docx"

                # Save with vendor name for download
                with open(po_filename, "wb") as f:
                    f.write(buffer.getvalue())

                # Save with PO number for approval system (with safe filename)
                with open(po_filename_for_approval, "wb") as f:
                    f.write(buffer.getvalue())

                # Offer download
                st.download_button(
                    label=f"Download {po_filename}",
                    data=buffer.getvalue(),
                    file_name=po_filename,
                    mime="application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                )

                # Send via WhatsApp using new workflow
                with st.spinner("Sending for approval..."):
                    try:
                        st.write(f"DEBUG: Sending PO {new_po_number_for_doc} for first approval")

                        # Use new function to save to temp folder, send, and delete
                        success = save_and_send_po_document(buffer, new_po_number_for_doc, "first")

                        if success:
                            st.success("Sent for first approval successfully!")
                        else:
                            st.error("Failed to send for approval")

                    except Exception as e:
                        st.error(f"Failed to send for approval: {str(e)}")

            except Exception as e:
                st.error(f"An error occurred during processing: {e}")
                st.info("Please ensure your Excel files have the correct columns and data format. Also, ensure your Word template table has enough columns for the item details (S.NO to Amount with GST).")
                st.exception(e) 

    elif app_mode == "Edit Existing PO":
        st.header("Edit Existing Purchase Order")

        if po_df.empty:
            st.warning("No existing Purchase Orders found in PO DB.xlsx. Please create a new PO first.")
            return

        po_numbers = ["Select an old PO"] + po_df["PO No."].tolist()
        selected_po_to_edit = st.selectbox("Select PO Number to Edit:", po_numbers, key="edit_po_select")

        if selected_po_to_edit == "Select an old PO":
            st.info("Please select an existing PO Number to load its details.")
            return

        # Retrieve details for the selected PO
        po_row_details = po_df[po_df["PO No."] == selected_po_to_edit].iloc[0]
        
        # Pre-fill subject, vendor, project, and delivery address type based on selected PO
        initial_subject = po_row_details.get("Subject", "Purchase of Electrical Items- for 33kv Transmission Line")
        initial_vendor_name = po_row_details.get("Vendor Name", "Select a Vendor")
        initial_project_name = po_row_details.get("Project Name", "Select a Project")
        initial_delivery_address_type = po_row_details.get("Delivery Address Type", "Project Address") # Retrieve saved address type
        initial_num_days_dispatch = po_row_details.get("Num Days Dispatch", 1) # Assuming you'd store this
        initial_num_years_warranty = po_row_details.get("Num Years Warranty", 1)
        initial_transit_insurance_scope = po_row_details.get("Transit Insurance Scope", "Vendor")
        initial_payment_terms_text = po_row_details.get("Payment Terms Text", "10% advance on PI with PO, balance 90% on dispatch of material with GST")

        subject_input = st.text_input("Enter Subject for the Purchase Order:", value=initial_subject, key="edit_po_subject")

        # Use columns for horizontal layout
        col1, col2, col3 = st.columns(3)

        with col1:
            vendor_names = ["Select a Vendor"] + vendor_df["Name"].tolist()
            # Set default index for vendor dropdown
            try:
                vendor_default_index = vendor_names.index(initial_vendor_name)
            except ValueError:
                vendor_default_index = 0 # Default to "Select a Vendor" if not found
            selected_vendor_name = st.selectbox("Select Vendor:", vendor_names, index=vendor_default_index, key="edit_po_vendor_select")

        with col2:
            project_names = ["Select a Project"] + project_db_df["Name"].tolist()
            # Set default index for project dropdown
            try:
                project_default_index = project_names.index(initial_project_name)
            except ValueError:
                project_default_index = 0 # Default to "Select a Project" if not found
            selected_project_name = st.selectbox("Select Project:", project_names, index=project_default_index, key="edit_po_project_select")

        with col3:
            delivery_address_options = ["Project Address", "Dwarka Office"]
            try:
                delivery_address_default_index = delivery_address_options.index(initial_delivery_address_type)
            except ValueError:
                delivery_address_default_index = 0 # Default to "Project Address" if not found
            selected_delivery_address_type = st.selectbox("Select Delivery Address Type:", delivery_address_options, index=delivery_address_default_index, key="edit_po_delivery_address_type")

        # Inco Term, Freight Charges, GST for Freight Charges in one row
        col_inco, col_freight, col_gst = st.columns(3)
        with col_inco:
            inco_term_options = ["FOR", "Ex works", "CIF", "CPT"]
            initial_inco_term = po_row_details.get("Inco Term", "FOR")
            try:
                inco_term_default_index = inco_term_options.index(initial_inco_term)
            except ValueError:
                inco_term_default_index = 0
            selected_inco_term = st.selectbox("Select Inco Term:", inco_term_options, index=inco_term_default_index, key="edit_po_inco_term")
        with col_freight:
            freight_charges_amount = None
            initial_freight_amount = po_row_details.get("Freight Amount", 0.0)
            # Ensure the value is a float to match min_value type
            initial_freight_amount = float(initial_freight_amount) if pd.notna(initial_freight_amount) else 0.0
            if selected_inco_term in ["Ex works", "CIF", "CPT"]:
                freight_charges_amount = st.number_input("Enter Freight Charges Amount (INR):", min_value=0.0, value=initial_freight_amount, key="edit_po_freight_amount")
        with col_gst:
            freight_gst_percent = None
            initial_freight_gst = po_row_details.get("Freight GST", "0%")
            if selected_inco_term in ["Ex works", "CIF", "CPT"]:
                gst_options = ["0%", "12%", "18%"]
                try:
                    freight_gst_default_index = gst_options.index(initial_freight_gst)
                except ValueError:
                    freight_gst_default_index = 0
                selected_freight_gst = st.selectbox("Select GST for Freight Charges:", gst_options, index=freight_gst_default_index, key="edit_po_freight_gst")
                freight_gst_percent = float(selected_freight_gst.replace('%', '')) / 100.0
            else:
                freight_gst_percent = None

        # Number of Days for Dispatch, Warranty Period, Transit Insurance Scope in one row
        col_days, col_warranty, col_insurance = st.columns(3)
        with col_days:
            initial_num_days_dispatch = po_row_details.get("Num Days Dispatch", 1)
            # Ensure the value is an integer to match min_value type
            initial_num_days_dispatch = int(initial_num_days_dispatch) if pd.notna(initial_num_days_dispatch) else 1
            num_days_dispatch = st.number_input("Enter Number of Days for Dispatch:", min_value=1, value=initial_num_days_dispatch, key="edit_po_num_days_dispatch")
        with col_warranty:
            initial_num_years_warranty = po_row_details.get("Num Years Warranty", 1)
            # Ensure the value is an integer to match min_value type
            initial_num_years_warranty = int(initial_num_years_warranty) if pd.notna(initial_num_years_warranty) else 1
            num_years_warranty = st.number_input("Enter Warranty Period (years):", min_value=1, value=initial_num_years_warranty, key="edit_po_num_years_warranty")
        with col_insurance:
            transit_insurance_options = ["Vendor", "MRL"]
            initial_transit_insurance_scope = po_row_details.get("Transit Insurance Scope", "Vendor")
            transit_insurance_scope = st.selectbox("Select Transit Insurance Scope:", transit_insurance_options, index=transit_insurance_options.index(initial_transit_insurance_scope) if initial_transit_insurance_scope in transit_insurance_options else 0, key="edit_po_transit_insurance_scope")

        payment_terms_options = [
            "10% advance on PI with PO, balance 90% on dispatch of material with GST",
            "20% advance on PI with PO, balance 80% on dispatch of material with GST",
            "30% advance on PI with PO, balance 70% on dispatch of material with GST",
            "50% advance on PI with PO, balance 50% on dispatch of material with GST",
            "100% payment before dispatch of material",
            "100% payment after delivery of material",
            "30 days credit payment",
            "15 days credit payment",
            "10% advance on PI, 90% balance on Letter of credit for 30 days.",
            "100% payment on Letter of credit (Irrevocable LC) for 30 days",
            "50% advance on PI, 50% balance on Letter of credit for 30 days.",
            "10% advance on PI, 70% balance against the submission of RA bills, 20% after handover of site.",
            "10% advance on PI, 80% balance against the submission of RA bills, 10% after handover of site.",
            "10% advance on PI, 80% balance before dispatch of material, 10% against submission of PBG till warranty period."
        ]
        payment_terms_text = st.selectbox("Select Payment Terms:", payment_terms_options, index=payment_terms_options.index(initial_payment_terms_text) if initial_payment_terms_text in payment_terms_options else 0, key="edit_po_payment_terms_text")


        purchase_items_excel_file = st.file_uploader("Upload Purchase Items Excel File (CSV, XLSX)", type=["csv", "xlsx"], key="edit_po_items_excel")

        # Conditional logic for processing
        if selected_vendor_name == "Select a Vendor" or selected_project_name == "Select a Project" or purchase_items_excel_file is None:
            st.info("Please select a Vendor, a Project, and upload the Purchase Items Excel file to proceed.")
            return

        # Filter dataframes based on selection
        selected_vendor_row = vendor_df[vendor_df["Name"] == selected_vendor_name].iloc[0]
        selected_project_row = project_db_df[project_db_df["Name"] == selected_project_name].iloc[0]

        st.info(f"You are editing PO Number: **{selected_po_to_edit}**")

        created_at_str = po_row_details.get("Created At", None)
        po_edit_allowed = True
        otp_verified = False

        # Method 1: Check "Created At" column if available
        if created_at_str and pd.notna(created_at_str):
            try:
                created_at_dt = datetime.strptime(str(created_at_str), "%Y-%m-%d %H:%M:%S")
                time_diff = datetime.now() - created_at_dt
                if time_diff.total_seconds() > 24*3600:
                    po_edit_allowed = False
            except Exception as e:
                # If parsing fails, fall back to Method 2
                created_at_str = None

        # Method 2: If "Created At" is empty, extract date from PO number
        if not created_at_str or pd.isna(created_at_str):
            try:
                # Extract date from PO number format: MRL/{project_code}/{DDMMYYYY}/{incremental}
                po_parts = selected_po_to_edit.split('/')
                if len(po_parts) >= 3:
                    po_date_str = po_parts[2]  # DDMMYYYY format
                    if len(po_date_str) == 8 and po_date_str.isdigit():
                        # Parse the date from DDMMYYYY format
                        po_date = datetime.strptime(po_date_str, "%d%m%Y").date()
                        today_date = datetime.now().date()

                        # If PO date is not today, it's older than 24 hours
                        if po_date != today_date:
                            po_edit_allowed = False
                    else:
                        # Invalid date format in PO number, allow edit (fail open)
                        po_edit_allowed = True
                else:
                    # Invalid PO number format, allow edit (fail open)
                    po_edit_allowed = True
            except Exception as e:
                # If date extraction fails, allow edit (fail open)
                po_edit_allowed = True
        if not po_edit_allowed:
            st.warning("This PO is older than 24 hours. OTP verification is required to edit.")
            # Add approver selection dropdown
            approver_options = {
                "Sameer Dua": "919811229207"
            }
            approver_names = list(approver_options.keys())
            selected_approver = st.selectbox("Select Approver for OTP:", approver_names, key="otp_approver_select")
            approver_number = approver_options[selected_approver]
            if 'otp_sent' not in st.session_state or st.session_state.get('otp_approver_number') != approver_number:
                import random
                otp_code = random.randint(100000, 999999)
                st.session_state['otp_code'] = otp_code
                st.session_state['otp_approver_number'] = approver_number
                formatted_content = f"Your OTP for editing PO {selected_po_to_edit} is: {otp_code}"
                url = "https://gate.whapi.cloud/messages/text"
                payload = {
                    "typing_time": 0,
                    "to": f"{approver_number}@s.whatsapp.net",
                    "body": formatted_content
                }
                headers = {
                    "accept": "application/json",
                    "content-type": "application/json",
                    "authorization": "Bearer UhMontkZC8KY8WBLgcrH7kz6HYXlntsZ"
                }
                try:
                    response = requests.post(url, json=payload, headers=headers)
                    if response.status_code == 200:
                        st.session_state['otp_sent'] = True
                        st.info(f"OTP sent to {selected_approver}'s WhatsApp. Please enter it below to proceed.")
                    else:
                        st.error("Failed to send OTP. Please try again later.")
                except Exception as e:
                    st.error(f"Error sending OTP: {e}")
            otp_input = st.text_input("Enter OTP sent to the selected approver's WhatsApp:", type="password")
            if st.button("Verify OTP"):
                if 'otp_code' in st.session_state and otp_input == str(st.session_state['otp_code']):
                    st.success("OTP verified. You may now edit this PO.")
                    otp_verified = True
                    st.session_state['otp_verified'] = True
                else:
                    st.error("Incorrect OTP. Please try again.")
        else:
            otp_verified = True  # Not needed for recent POs
            st.session_state['otp_verified'] = True
        # Only allow editing if within 24 hours or OTP verified
        if not otp_verified:
            st.stop()

        if st.button("Generate & Download Edited Purchase Order"):
            try:
                # Read Purchase Items Excel data
                if purchase_items_excel_file.name.endswith('.csv'):
                    purchase_items_df = pd.read_csv(purchase_items_excel_file)
                else:
                    purchase_items_df = pd.read_excel(purchase_items_excel_file)
                
                # Validation checks (as before)
                required_vendor_columns = ["Name", "Address", "GST No", "Contact Person", "Contact No 1", "Contact No 2", "Email"]
                if not all(col in vendor_df.columns for col in required_vendor_columns):
                    st.error(f"Vendor Details Excel file must contain all of these columns: {', '.join(required_vendor_columns)}")
                    return

                required_gst = ["S.NO", "Description of Item", "Qty", "UOM", "Unit Price", "Taxable Amount (INR)", "GST", "GST Amount (INR)", "Total Amount (INR)"]
                required_cgst_sgst = ["S.NO", "Description of Item", "Qty", "UOM", "Unit Price", "Taxable Amount (INR)", "CGST", "SGST", "GST Amount (INR)", "Total Amount (INR)"]
                if not (all(col in purchase_items_df.columns for col in required_gst) or all(col in purchase_items_df.columns for col in required_cgst_sgst)):
                    st.error(
                        "Purchase Items Excel file must contain either:\n"
                        f"{', '.join(required_gst)}\n"
                        "OR\n"
                        f"{', '.join(required_cgst_sgst)}"
                    )
                    return

                required_project_columns = ["Name", "Address", "GST No", "Code"] 
                if not all(col in project_db_df.columns for col in required_project_columns):
                    st.error(f"Project DB Excel file must contain all of these columns: {', '.join(required_project_columns)}. Missing: {', '.join(set(required_project_columns) - set(project_db_df.columns))}")
                    return

                # Ensure relevant columns in purchase_items_df are numeric for processing
                purchase_items_df["Taxable Amount (INR)"] = pd.to_numeric(purchase_items_df["Taxable Amount (INR)"], errors='coerce').fillna(0)
                purchase_items_df["GST Amount (INR)"] = pd.to_numeric(purchase_items_df["GST Amount (INR)"], errors='coerce').fillna(0)
                purchase_items_df["Total Amount (INR)"] = pd.to_numeric(purchase_items_df["Total Amount (INR)"], errors='coerce').fillna(0)
                if "GST" in purchase_items_df.columns:
                    purchase_items_df["GST"] = pd.to_numeric(purchase_items_df["GST"], errors='coerce').fillna(0)
                if "CGST" in purchase_items_df.columns:
                    purchase_items_df["CGST"] = pd.to_numeric(purchase_items_df["CGST"], errors='coerce').fillna(0)
                if "SGST" in purchase_items_df.columns:
                    purchase_items_df["SGST"] = pd.to_numeric(purchase_items_df["SGST"], errors='coerce').fillna(0)

                # Use selected vendor and project row data
                vendor_name = str(selected_vendor_row["Name"]).strip() if pd.notna(selected_vendor_row["Name"]) else ""
                vendor_address = str(selected_vendor_row["Address"]).strip() if pd.notna(selected_vendor_row["Address"]) else ""
                vendor_gst = str(selected_vendor_row["GST No"]).strip() if pd.notna(selected_vendor_row["GST No"]) else ""
                contact_person = str(selected_vendor_row["Contact Person"]).strip() if pd.notna(selected_vendor_row["Contact Person"]) else ""
                contact_no1 = str(selected_vendor_row["Contact No 1"]).strip() if pd.notna(selected_vendor_row["Contact No 1"]) else ""
                contact_no2 = str(selected_vendor_row["Contact No 2"]).strip() if pd.notna(selected_vendor_row["Contact No 2"]) else ""
                vendor_email = str(selected_vendor_row["Email"]).strip() if pd.notna(selected_vendor_row["Email"]) else ""

                combined_contact_no = ""
                if contact_no1 and contact_no2:
                    combined_contact_no = f"{contact_no1}, {contact_no2}"
                elif contact_no1:
                    combined_contact_no = contact_no1
                elif contact_no2:
                    combined_contact_no = contact_no2

                project_name = str(selected_project_row["Name"]).strip() if pd.notna(selected_project_row["Name"]) else ""
                project_address = str(selected_project_row["Address"]).strip() if pd.notna(selected_project_row["Address"]) else ""
                project_gst = str(selected_project_row["GST No"]).strip() if pd.notna(selected_project_row["GST No"]) else ""
                project_code = str(selected_project_row["Code"]).strip() if pd.notna(selected_project_row["Code"]) else "UNKNOWN_CODE"

                # Determine the actual delivery address based on selection
                final_delivery_address = ""
                if selected_delivery_address_type == "Project Address":
                    final_delivery_address = project_address
                elif selected_delivery_address_type == "Dwarka Office":
                    final_delivery_address = dwarka_office_address


                # Calculate totals from Purchase Items DataFrame using new column names
                total_basic_amount = purchase_items_df["Taxable Amount (INR)"].sum()
                grand_total = purchase_items_df["Total Amount (INR)"].sum()
                gst_amount = purchase_items_df["GST Amount (INR)"].sum()
                
                amount_in_words = num_to_words(int(grand_total)) 

                total_amount_details = {
                    "total_basic_amount": total_basic_amount,
                    "gst_amount": gst_amount,
                    "grand_total": grand_total,
                    "amount_in_words": amount_in_words
                }

                # Determine GST percentage text for replacement
                if "GST" in purchase_items_df.columns:
                    unique_gst_values = purchase_items_df["GST"].unique()
                    present_gst_percentages = set()
                    for gst_val in unique_gst_values:
                        if pd.notna(gst_val) and gst_val != 0:
                            try:
                                percent_val = int(float(gst_val) * 100)
                                if percent_val == 18:
                                    present_gst_percentages.add(18)
                                elif percent_val == 12:
                                    present_gst_percentages.add(12)
                            except ValueError:
                                pass
                    gst_display_parts = []
                    if 18 in present_gst_percentages:
                        gst_display_parts.append("@18%")
                    if 12 in present_gst_percentages:
                        gst_display_parts.append("@12%")
                    gst_display_parts.sort(key=lambda x: int(x.replace('@', '').replace('%', '')))
                    if len(gst_display_parts) == 2:
                        gst_text_replacement = "GST " + " & ".join(gst_display_parts)
                    elif len(gst_display_parts) == 1:
                        gst_text_replacement = "GST " + gst_display_parts[0]
                    else:
                        gst_text_replacement = "GST"
                elif "CGST" in purchase_items_df.columns and "SGST" in purchase_items_df.columns:
                    cgst_vals = purchase_items_df["CGST"].unique()
                    sgst_vals = purchase_items_df["SGST"].unique()
                    cgst_percent = set()
                    sgst_percent = set()
                    for val in cgst_vals:
                        if pd.notna(val) and val != 0:
                            try:
                                cgst_percent.add(int(float(val) * 100))
                            except ValueError:
                                pass
                    for val in sgst_vals:
                        if pd.notna(val) and val != 0:
                            try:
                                sgst_percent.add(int(float(val) * 100))
                            except ValueError:
                                pass
                    cgst_str = ', '.join([f'@{v}% CGST' for v in sorted(cgst_percent)])
                    sgst_str = ', '.join([f'@{v}% SGST' for v in sorted(sgst_percent)])
                    if cgst_str and sgst_str:
                        gst_text_replacement = f'GST {cgst_str} & {sgst_str}'
                    elif cgst_str:
                        gst_text_replacement = f'GST {cgst_str}'
                    elif sgst_str:
                        gst_text_replacement = f'GST {sgst_str}'
                    else:
                        gst_text_replacement = 'GST'
                else:
                    gst_text_replacement = 'GST'

                # Use the selected PO number for the document
                po_number_for_doc = selected_po_to_edit

                # No update to PO DB in edit mode

                # Define replacements for the current vendor and project
                replacements = {
                    "M/s Goyal Electric Works": f"M/s {vendor_name}",
                    "1541/D34, 1st floor, Krishna Market, Bhagirath Palace, Delhi 110006": vendor_address,
                    "07AALPJ3113A1ZD": vendor_gst,
                    "JYOTI RAGHAV": contact_person,
                    "8368554622": combined_contact_no,
                    "<EMAIL>": vendor_email,
                    "27-06-2025": datetime.now().strftime("%d-%m-%Y"), 
                    "Purchase of Electrical Items- for 33kv Transmission Line": subject_input,
                    "GST @18% & 12%": gst_text_replacement, 
                    
                    # Project DB replacements - these are now handled specifically in replace_text_in_docx
                    "Evincea Renewables (Three) Pvt Ltd  - 2.5 MW (TVS)": project_name,
                    "05AAHCE8670N1Z2": project_gst,
                    "MRL/ERPL3/27062025/064": po_number_for_doc,
                    "Purchase Order": "Amended Purchase Order" # Set to "Amended Purchase Order" for edit mode
                }

                # Apply replacements and table update
                modified_doc = replace_text_in_docx(
                    io.BytesIO(word_template_bytes), 
                    replacements,
                    purchase_items_df,
                    total_amount_details,
                    final_delivery_address, # Pass the determined delivery address
                    project_name, # Pass the project name from DB
                    selected_inco_term, # Pass selected Inco Term
                    freight_charges_amount, # Pass freight charges amount
                    freight_gst_percent, # Pass freight GST percentage
                    num_days_dispatch, # Pass number of days for dispatch
                    num_years_warranty, # Pass number of years for warranty
                    transit_insurance_scope, # Pass transit insurance scope
                    payment_terms_text # Pass payment terms text
                )

                # Save the modified document to a BytesIO object
                buffer = io.BytesIO()
                modified_doc.save(buffer)
                buffer.seek(0)

                st.subheader("Download Modified Word Document:")
                st.download_button(
                    label=f"Download Purchase Order - {selected_po_to_edit}.docx",
                    data=buffer.getvalue(),
                    file_name=f"Purchase Order - {selected_po_to_edit}.docx",
                    mime="application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                )

            except Exception as e:
                st.error(f"An error occurred during processing: {e}")
                st.info("Please ensure your Excel files have the correct columns and data format. Also, ensure your Word template table has enough columns for the item details (S.NO to Amount with GST).")
                st.exception(e)

def send_document_for_approval(doc_base64, po_number, approval_stage):
    """Send document for approval via WhatsApp API"""
    # Determine phone number based on approval stage
    if approval_stage == "first":
        phone_number = "919599113982"  # First approver
    else:
        phone_number = "919599113982"  # Second approver (same for now)

    # Document upload
    doc_url = "https://gate.whapi.cloud/messages/document"
    doc_payload = {
        "to": f"{phone_number}@s.whatsapp.net",
        "media": doc_base64,
        "caption": f"Please approve PO: {po_number} (Stage: {approval_stage})"
    }
    doc_headers = {
        "accept": "application/json",
        "content-type": "application/json",
        "authorization": "Bearer UhMontkZC8KY8WBLgcrH7kz6HYXlntsZ"
    }

    try:
        doc_response = requests.post(doc_url, json=doc_payload, headers=doc_headers)

        if doc_response.status_code != 200:
            raise Exception(f"Document upload failed: {doc_response.text}")

        # Send approval buttons
        button_url = "https://gate.whapi.cloud/messages/interactive"
        button_payload = {
            "to": f"{phone_number}@s.whatsapp.net",
            "type": "interactive",
            "interactive": {
                "type": "button",
                "body": {
                    "text": f"Please review and approve/disapprove PO: {po_number}"
                },
                "action": {
                    "buttons": [
                        {
                            "type": "reply",
                            "reply": {
                                "id": "approve",
                                "title": "Approve"
                            }
                        },
                        {
                            "type": "reply",
                            "reply": {
                                "id": "disapprove",
                                "title": "Disapprove"
                            }
                        }
                    ]
                }
            }
        }
        button_headers = {
            "accept": "application/json",
            "content-type": "application/json",
            "authorization": "Bearer UhMontkZC8KY8WBLgcrH7kz6HYXlntsZ"
        }

        button_response = requests.post(button_url, json=button_payload, headers=button_headers)

        if button_response.status_code != 200:
            raise Exception(f"Buttons failed: {button_response.text}")

        return True

    except Exception as e:
        print(f"ERROR: Failed to send document for approval: {str(e)}")
        return False

def add_signature_to_doc(doc_stream, signature_image_path, signature_name):
    """Add signature image to Word document above 'AUTHORISED SIGNATORY'"""
    document = Document(doc_stream)
    
    # Find the paragraph containing "AUTHORISED SIGNATORY"
    for paragraph in document.paragraphs:
        if "AUTHORISED SIGNATORY" in paragraph.text:
            # Add signature image before this paragraph
            run = paragraph.insert_paragraph_before().add_run()
            run.add_picture(signature_image_path, width=Inches(1.5))
            
            # Add signature name
            signature_para = paragraph.insert_paragraph_before()
            signature_para.add_run(f"{signature_name}\n").bold = True
            break
    
    # Save modified document
    buffer = io.BytesIO()
    document.save(buffer)
    buffer.seek(0)
    return buffer

def handle_approval_response(po_number, approval_stage, is_approved):
    """Update PO DB based on approval response"""
    po_db_path = "PO DB.xlsx"  # Define the path here to match main function
    po_df = pd.read_excel(po_db_path)
    
    if approval_stage == "first":
        status = "Approved" if is_approved else "Disapproved"
        po_df.loc[po_df["PO No."] == po_number, "Approval 1"] = status
        
        if is_approved:
            # Send to second approver using new workflow
            try:
                # Use safe filename format
                safe_po_number = sanitize_filename(po_number)
                filename = f"Purchase Order - {safe_po_number}.docx"

                with open(filename, "rb") as f:
                    doc_bytes = f.read()

                # Create buffer for the new workflow
                buffer = io.BytesIO(doc_bytes)

                # Use new function to save to temp folder, send, and delete
                success = save_and_send_po_document(buffer, po_number, "second")

                if success:
                    po_df.loc[po_df["PO No."] == po_number, "Approval 2"] = "Pending"
                else:
                    print(f"Failed to send PO {po_number} for second approval")

            except Exception as e:
                print(f"Error sending PO {po_number} for second approval: {str(e)}")
    else:
        status = "Approved" if is_approved else "Disapproved"
        po_df.loc[po_df["PO No."] == po_number, "Approval 2"] = status
        
        if is_approved:
            # Add second signature
            safe_po_number = sanitize_filename(po_number)
            filename = f"Purchase Order - {safe_po_number}.docx"
            signed_filename = f"Purchase Order - {safe_po_number} (Signed).docx"

            with open(filename, "rb") as f:
                doc_stream = io.BytesIO(f.read())
            signed_doc = add_signature_to_doc(doc_stream, "SameerDua.png", "Sameer Dua")

            # Save final signed document
            with open(signed_filename, "wb") as f:
                f.write(signed_doc.getvalue())
    
    po_df.to_excel(po_db_path, index=False)

if __name__ == "__main__":
    main()
